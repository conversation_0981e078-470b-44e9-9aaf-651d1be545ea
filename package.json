{"name": "trackit", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "npm-run-all --parallel dev:frontend dev:backend", "dev:frontend": "react-router dev", "dev:backend": "convex dev", "predev": "convex dev --until-success && convex dev --once --run-sh \"node setup.mjs --once\" && convex dashboard", "build": "react-router build", "lint": "tsc && eslint .  --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "netlify serve", "typecheck": "react-router typegen && tsc", "generatekeys": "node generatekeys.mjs"}, "dependencies": {"@convex-dev/auth": "^0.0.81", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@react-router/node": "^7.6.1", "@react-router/serve": "^7.6.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.23.0", "isbot": "^5", "jose": "^6.0.11", "lucide-react": "^0.510.0", "netlify-cli": "^21.5.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router": "^7.6.1", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@react-router/dev": "^7.6.1", "@tailwindcss/vite": "^4.0.14", "@types/node": "^22.13.10", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "dotenv": "^16.4.7", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "npm-run-all": "^4.1.5", "prettier": "^3.5.3", "tailwindcss": "^4.0.14", "tw-animate-css": "^1.2.9", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}, "pnpm": {"onlyBuiltDependencies": ["@tailwindcss/oxide", "esbuild"]}}