// Generated by React Router

import "react-router"

declare module "react-router" {
  interface Register {
    pages: Pages
    routeFiles: RouteFiles
  }
}

type Pages = {
  "/": {
    params: {};
  };
  "/tasks/:id": {
    params: {
      "id": string;
    };
  };
};

type RouteFiles = {
  "root.tsx": {
    id: "root";
    page: "/" | "/tasks/:id";
  };
  "routes/home.tsx": {
    id: "routes/home";
    page: "/";
  };
  "routes/taskDetails.tsx": {
    id: "routes/taskDetails";
    page: "/tasks/:id";
  };
};